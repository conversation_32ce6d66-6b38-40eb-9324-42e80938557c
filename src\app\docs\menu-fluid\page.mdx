import { CodeBlock } from "@/components/site/code-block";
import { ComponentRenderer } from "@/components/site/component-renderer";
import { Cli } from "@/components/site/cli";
import {
  Tabs,
  TabsList,
  TabsTrigger,
  TabsContent,
} from "@/components/site/tabs.tsx";
import { PropsTable } from "@/components/site/props-table.tsx";

import MenuFluidPreview from "./menu-fluid-preview";

# Menu Fluid

A tabular navigation with seamless fluid hover animation.

## Preview

<Tabs defaultValue="preview" className="">
  <TabsList>
    <TabsTrigger value="preview">Preview</TabsTrigger>
    <TabsTrigger value="code">Code</TabsTrigger>
  </TabsList>
  <TabsContent value="preview">
    <ComponentRenderer component={<MenuFluidPreview />} />
  </TabsContent>
  <TabsContent value="code">
    <CodeBlock filePath="src/app/docs/menu-fluid/menu-fluid-preview.tsx" />
  </TabsContent>
</Tabs>

## Installation

<Tabs defaultValue="cli" className="">
  <TabsList>
    <TabsTrigger value="cli">CLI</TabsTrigger>
    <TabsTrigger value="manual">Manual</TabsTrigger>
  </TabsList>
  <TabsContent value="cli">
    <Cli command={`add "https://berlix.vercel.app/registry/menu-fluid.json"`} />
  </TabsContent>

  <TabsContent value="manual">
    <CodeBlock filePath="src/components/core/menu-fluid.tsx" />
  </TabsContent>
</Tabs>

## Component Props

<PropsTable
  props={[
    {
      name: "menuItems",
      type: "MenuItem[]",
      required: true,
      description:
        "An array of menu items, each with a label (string) and href (string).",
    },
    {
      name: "className",
      type: "string",
      required: false,
      description: "Optional additional CSS classes to apply to the container.",
    },
    {
      name: "indicatorClassName",
      type: "string",
      required: false,
      description:
        "Optional additional CSS classes to apply to the hovered background indicator.",
    },
  ]}
/>
