import { CodeB<PERSON> } from "@/components/site/code-block";
import { ComponentRenderer } from "@/components/site/component-renderer";
import { Cli } from "@/components/site/cli";
import {
  Tabs,
  TabsList,
  TabsTrigger,
  TabsContent,
} from "@/components/site/tabs.tsx";
import { PropsTable } from "@/components/site/props-table.tsx";

import FlipCardBasic from "./flip-card-basic";
import FlipCardVertical from "./flip-card-vertical";

# Flip Card

A 3D animated flip card component that flips on hover, customizable by direction, rotation style, and transition duration.

## Examples

### Flip Card Basic

Basic ussage using the default values.

<Tabs defaultValue="preview" className="">
  <TabsList>
    <TabsTrigger value="preview">Preview</TabsTrigger>
    <TabsTrigger value="code">Code</TabsTrigger>
  </TabsList>
  <TabsContent value="preview">
    <ComponentRenderer component={<FlipCardBasic />} />
  </TabsContent>
  <TabsContent value="code">
    <CodeBlock filePath="src/app/docs/flip-card/flip-card-basic.tsx" />
  </TabsContent>
</Tabs>

### Flip Card Custom

Example using vertical flip direction and reverse rotation

<Tabs defaultValue="preview" className="">
  <TabsList>
    <TabsTrigger value="preview">Preview</TabsTrigger>
    <TabsTrigger value="code">Code</TabsTrigger>
  </TabsList>
  <TabsContent value="preview">
    <ComponentRenderer component={<FlipCardVertical />} />
  </TabsContent>
  <TabsContent value="code">
    <CodeBlock filePath="src/app/docs/flip-card/flip-card-vertical.tsx" />
  </TabsContent>
</Tabs>

## Installation

<Tabs defaultValue="cli" className="">
  <TabsList>
    <TabsTrigger value="cli">CLI</TabsTrigger>
    <TabsTrigger value="manual">Manual</TabsTrigger>
  </TabsList>
  <TabsContent value="cli">
    <Cli command={`add "https://berlix.vercel.app/registry/flip-card.json"`} />
  </TabsContent>

  <TabsContent value="manual">
    <CodeBlock filePath="src/components/core/flip-card.tsx" />
  </TabsContent>
</Tabs>

## Component Props

<PropsTable
  props={[
    {
      name: "front",
      type: "ReactNode",
      required: true,
      description: "The content to be displayed on the front side of the card.",
    },
    {
      name: "back",
      type: "ReactNode",
      required: true,
      description: "The content to be displayed on the back side of the card.",
    },
    {
      name: "duration",
      type: "number",
      default: 0.3,
      description:
        "Duration of the flip animation in seconds.",
    },
    {
      name: "flipDirection",
      type: `'horizontal' | 'vertical'`,
      default: `'horizontal'`,
      description: "Direction of the flip.",
    },
    {
      name: "flipRotation",
      type: `'forward' | 'reverse'`,
      default: `'forward'`,
      description: "Flip rotation direction.",
    },
    {
      name: "className",
      type: "string",
      description: "Custom class name for the outer card container.",
    },
    {
      name: "panelClassName",
      type: "string",
      description: "Custom class name for both front and back panels.",
    },

]}
/>
