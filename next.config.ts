import type { NextConfig } from "next";
import createMDX from "@next/mdx";

const nextConfig: NextConfig = {
  /* config options here */
  reactStrictMode: true,
  pageExtensions: ["js", "jsx", "ts", "tsx", "md", "mdx"],
};

const withMDX = createMDX({
  extension: /\.(md|mdx)$/,
  options: {
    remarkPlugins: [
      // Add remark-gfm for GitHub Flavored Markdown support
      [require('remark-gfm'), {}]
    ],
    rehypePlugins: [],
  },
});

export default withMDX(nextConfig);
