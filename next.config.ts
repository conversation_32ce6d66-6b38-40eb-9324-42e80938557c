import type { NextConfig } from "next";
import createMDX from "@next/mdx";
import remarkGfm from "remark-gfm";
import { createHighlighter } from "shiki";

const nextConfig: NextConfig = {
  /* config options here */
  reactStrictMode: true,
  pageExtensions: ["js", "jsx", "ts", "tsx", "md", "mdx"],
};

const withMDX = createMDX({
  extension: /\.(md|mdx)$/,
  options: {
    remarkPlugins: [remarkGfm],
    rehypePlugins: [
      [
        // @ts-ignore
        async () => {
          const highlighter = await createHighlighter({
            themes: ["github-light", "github-dark"],
            langs: [
              "javascript",
              "typescript",
              "jsx",
              "tsx",
              "css",
              "html",
              "json",
              "bash",
              "shell",
              "python",
              "rust",
              "go",
              "java",
              "c",
              "cpp",
              "php",
              "ruby",
              "swift",
              "kotlin",
              "dart",
              "sql",
              "yaml",
              "toml",
              "xml",
              "markdown",
            ],
          });

          return (tree: any) => {
            // This will be handled by our CodeBlock component
            return tree;
          };
        },
      ],
    ],
  },
});

export default withMDX(nextConfig);
