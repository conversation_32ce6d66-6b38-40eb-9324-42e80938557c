import { CodeB<PERSON> } from "@/components/site/code-block";
import { ComponentRenderer } from "@/components/site/component-renderer";
import { Cli } from "@/components/site/cli";
import {
  Tabs,
  TabsList,
  TabsTrigger,
  TabsContent,
} from "@/components/site/tabs.tsx";
import { PropsTable } from "@/components/site/props-table.tsx";

import GradientBarsPreview from "./gradient-bars-preview";

# Gradient Bars

Animated background with vertical gradient bars that pulse in a wave-like motion.

## Preview

<Tabs defaultValue="preview" className="">
  <TabsList>
    <TabsTrigger value="preview">Preview</TabsTrigger>
    <TabsTrigger value="code">Code</TabsTrigger>
  </TabsList>
  <TabsContent value="preview">
    <ComponentRenderer component={<GradientBarsPreview />} />
  </TabsContent>
  <TabsContent value="code">
    <CodeBlock filePath="src/app/docs/gradient-bars/gradient-bars-preview.tsx" />
  </TabsContent>
</Tabs>

## Installation

<Tabs defaultValue="cli" className="">
  <TabsList>
    <TabsTrigger value="cli">CLI</TabsTrigger>
    <TabsTrigger value="manual">Manual</TabsTrigger>
  </TabsList>
  <TabsContent value="cli">
    <Cli command={`add "https://berlix.vercel.app/registry/gradient-bars.json"`} />
  </TabsContent>

  <TabsContent value="manual">
    <CodeBlock filePath="src/components/core/gradient-bars.tsx" />
  </TabsContent>
</Tabs>

## Component Props

<PropsTable
  props={[
    {
      name: "bars",
      type: "number",
      default: 20,
      description: "The number of vertical bars to render in the animation.",
    },
    {
      name: "colors",
      type: "string[]",
      default: `['#8f0feb', 'transparent']`,
      description:
        "An array of color strings for gradient background for each bar.",
    },
  ]}
/>
