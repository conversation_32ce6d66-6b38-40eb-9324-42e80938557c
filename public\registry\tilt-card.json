{"$schema": "https://ui.shadcn.com/schema/registry-item.json", "name": "tilt-card", "title": "Tilt Card", "description": "A 3D interactive card component that responds to mouse movement with smooth tilt and elevation effects.", "author": "Reche Soares", "type": "registry:ui", "dependencies": ["motion"], "devDependencies": [], "registryDependencies": [], "cssVars": {"dark": {}, "light": {}}, "files": [{"path": "tilt-card.tsx", "content": "\"use client\";\n\nimport { cn } from \"@/lib/utils\";\nimport { motion, useMotionValue, useSpring, useTransform } from \"motion/react\";\nimport { useRef } from \"react\";\n\ninterface TiltCardProps {\n  range?: number;\n  depth?: number;\n  containerClassName?: string;\n  children: React.ReactNode;\n}\n\nexport const TiltCard = ({\n  range = 32,\n  depth = 80,\n  containerClassName,\n  children,\n}: TiltCardProps) => {\n  const ref = useRef<HTMLDivElement | null>(null);\n\n  const x = useMotionValue(0);\n  const y = useMotionValue(0);\n  const z = useMotionValue(0);\n\n  const xSpring = useSpring(x, { stiffness: 200, damping: 20 });\n  const ySpring = useSpring(y, { stiffness: 200, damping: 20 });\n  const zSpring = useSpring(z, { stiffness: 200, damping: 30 });\n\n  const handleMouseMove = (e: React.MouseEvent<HTMLDivElement, MouseEvent>) => {\n    if (!ref.current) return;\n\n    const rect = ref.current.getBoundingClientRect();\n    const width = rect.width;\n    const height = rect.height;\n\n    const mouseX = (e.clientX - rect.left) * range;\n    const mouseY = (e.clientY - rect.top) * range;\n\n    const rotationX = (mouseY / height - range / 2) * -1;\n    const rotationY = mouseX / width - range / 2;\n\n    x.set(rotationX);\n    y.set(rotationY);\n  };\n\n  const handleMouseEnter = () => {\n    z.set(depth);\n  };\n\n  const handleMouseLeave = () => {\n    x.set(0);\n    y.set(0);\n    z.set(3);\n  };\n\n  const shadow = useTransform(\n    zSpring,\n    (z) => `0 ${z / 2}px ${z}px rgba(0, 0, 0, 0.15)`\n  );\n\n  return (\n    <motion.div\n      ref={ref}\n      onMouseMove={handleMouseMove}\n      onMouseEnter={handleMouseEnter}\n      onMouseLeave={handleMouseLeave}\n      style={{\n        transformStyle: \"preserve-3d\",\n        rotateX: xSpring,\n        rotateY: ySpring,\n      }}\n      className=\"relative h-96 w-72 rounded-2xl bg-zinc-200 dark:bg-zinc-700 shadow-md border border-zinc-300 dark:border-zinc-600\"\n    >\n      <motion.div\n        style={{\n          transformStyle: \"preserve-3d\",\n          translateZ: zSpring,\n          boxShadow: shadow,\n        }}\n        className={cn(\n          \"absolute inset-4 rounded-xl bg-white overflow-hidden [transform-style:preserve-3d]\",\n          containerClassName\n        )}\n      >\n        {children}\n      </motion.div>\n    </motion.div>\n  );\n};\n", "type": "registry:ui"}]}