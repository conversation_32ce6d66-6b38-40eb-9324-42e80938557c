import type { MDXComponents } from "mdx/types";
import { createSlug } from "./lib/utils";
import { cn } from "./lib/utils";
import Link from "next/link";
import Image from "next/image";

export function useMDXComponents(components: MDXComponents): MDXComponents {
  return {
    ...components,

    // Headings with proper typography and anchor links
    h1: ({ children, ...props }: React.ComponentProps<"h1">) => {
      const id = createSlug(children?.toString() || "");
      return (
        <h1
          id={id}
          className="scroll-m-20 text-4xl font-extrabold tracking-tight lg:text-5xl mb-6 mt-8 first:mt-0 group"
          {...props}
        >
          <a href={`#${id}`} className="no-underline hover:underline">
            {children}
          </a>
        </h1>
      );
    },

    h2: ({ children, ...props }: React.ComponentProps<"h2">) => {
      const id = createSlug(children?.toString() || "");
      return (
        <h2
          id={id}
          className="scroll-m-20 border-b pb-2 text-3xl font-semibold tracking-tight mb-4 mt-8 first:mt-0 group"
          {...props}
        >
          <a href={`#${id}`} className="no-underline hover:underline">
            {children}
          </a>
        </h2>
      );
    },

    h3: ({ children, ...props }: React.ComponentProps<"h3">) => {
      const id = createSlug(children?.toString() || "");
      return (
        <h3
          id={id}
          className="scroll-m-20 text-2xl font-semibold tracking-tight mb-3 mt-6 first:mt-0 group"
          {...props}
        >
          <a href={`#${id}`} className="no-underline hover:underline">
            {children}
          </a>
        </h3>
      );
    },

    h4: ({ children, ...props }: React.ComponentProps<"h4">) => {
      const id = createSlug(children?.toString() || "");
      return (
        <h4
          id={id}
          className="scroll-m-20 text-xl font-semibold tracking-tight mb-2 mt-4 first:mt-0 group"
          {...props}
        >
          <a href={`#${id}`} className="no-underline hover:underline">
            {children}
          </a>
        </h4>
      );
    },

    h5: ({ children, ...props }: React.ComponentProps<"h5">) => {
      const id = createSlug(children?.toString() || "");
      return (
        <h5
          id={id}
          className="scroll-m-20 text-lg font-semibold tracking-tight mb-2 mt-4 first:mt-0 group"
          {...props}
        >
          <a href={`#${id}`} className="no-underline hover:underline">
            {children}
          </a>
        </h5>
      );
    },

    h6: ({ children, ...props }: React.ComponentProps<"h6">) => {
      const id = createSlug(children?.toString() || "");
      return (
        <h6
          id={id}
          className="scroll-m-20 text-base font-semibold tracking-tight mb-2 mt-4 first:mt-0 group"
          {...props}
        >
          <a href={`#${id}`} className="no-underline hover:underline">
            {children}
          </a>
        </h6>
      );
    },

    // Paragraph with proper spacing
    p: ({ children, ...props }: React.ComponentProps<"p">) => {
      return (
        <p
          className="leading-7 [&:not(:first-child)]:mt-6 text-muted-foreground"
          {...props}
        >
          {children}
        </p>
      );
    },

    // Links with proper styling
    a: ({ children, href, ...props }: React.ComponentProps<"a">) => {
      const isExternal = href?.startsWith('http') || href?.startsWith('//');
      const isAnchor = href?.startsWith('#');

      if (isAnchor || isExternal) {
        return (
          <a
            href={href}
            className="font-medium text-primary underline underline-offset-4 hover:text-primary/80 transition-colors"
            target={isExternal ? "_blank" : undefined}
            rel={isExternal ? "noopener noreferrer" : undefined}
            {...props}
          >
            {children}
          </a>
        );
      }

      return (
        <Link
          href={href || "#"}
          className="font-medium text-primary underline underline-offset-4 hover:text-primary/80 transition-colors"
          {...props}
        >
          {children}
        </Link>
      );
    },

    // Lists with proper styling
    ul: ({ children, ...props }: React.ComponentProps<"ul">) => {
      return (
        <ul
          className="my-6 ml-6 list-disc [&>li]:mt-2"
          {...props}
        >
          {children}
        </ul>
      );
    },

    ol: ({ children, ...props }: React.ComponentProps<"ol">) => {
      return (
        <ol
          className="my-6 ml-6 list-decimal [&>li]:mt-2"
          {...props}
        >
          {children}
        </ol>
      );
    },

    li: ({ children, ...props }: React.ComponentProps<"li">) => {
      return (
        <li
          className="text-muted-foreground"
          {...props}
        >
          {children}
        </li>
      );
    },

    // Blockquote with beautiful styling
    blockquote: ({ children, ...props }: React.ComponentProps<"blockquote">) => {
      return (
        <blockquote
          className="mt-6 border-l-2 border-primary pl-6 italic text-muted-foreground"
          {...props}
        >
          {children}
        </blockquote>
      );
    },

    // Table with responsive design
    table: ({ children, ...props }: React.ComponentProps<"table">) => {
      return (
        <div className="my-6 w-full overflow-y-auto">
          <table
            className="w-full border-collapse border border-border"
            {...props}
          >
            {children}
          </table>
        </div>
      );
    },

    thead: ({ children, ...props }: React.ComponentProps<"thead">) => {
      return (
        <thead
          className="bg-muted/50"
          {...props}
        >
          {children}
        </thead>
      );
    },

    tbody: ({ children, ...props }: React.ComponentProps<"tbody">) => {
      return (
        <tbody {...props}>
          {children}
        </tbody>
      );
    },

    tr: ({ children, ...props }: React.ComponentProps<"tr">) => {
      return (
        <tr
          className="border-b border-border transition-colors hover:bg-muted/50"
          {...props}
        >
          {children}
        </tr>
      );
    },

    th: ({ children, ...props }: React.ComponentProps<"th">) => {
      return (
        <th
          className="border border-border px-4 py-2 text-left font-bold [&[align=center]]:text-center [&[align=right]]:text-right"
          {...props}
        >
          {children}
        </th>
      );
    },

    td: ({ children, ...props }: React.ComponentProps<"td">) => {
      return (
        <td
          className="border border-border px-4 py-2 text-left [&[align=center]]:text-center [&[align=right]]:text-right"
          {...props}
        >
          {children}
        </td>
      );
    },

    // Code elements
    code: ({ children, className, ...props }: React.ComponentProps<"code">) => {
      const isInline = !className?.includes('language-');

      if (isInline) {
        return (
          <code
            className="relative rounded bg-muted px-[0.3rem] py-[0.2rem] font-mono text-sm font-semibold"
            {...props}
          >
            {children}
          </code>
        );
      }

      return <code className={className} {...props}>{children}</code>;
    },

    pre: ({ children, ...props }: React.ComponentProps<"pre">) => {
      return (
        <div className="relative group my-6">
          <pre
            className={cn(
              "overflow-x-auto rounded-lg border border-border bg-muted p-4 font-mono text-sm",
              "scrollbar-thin scrollbar-track-transparent scrollbar-thumb-border"
            )}
            {...props}
          >
            {children}
          </pre>
        </div>
      );
    },

    // Horizontal rule
    hr: ({ ...props }: React.ComponentProps<"hr">) => {
      return (
        <hr
          className="my-4 border-border"
          {...props}
        />
      );
    },

    // Images with Next.js optimization
    img: ({ src, alt, ...props }: React.ComponentProps<"img">) => {
      if (!src) return null;

      return (
        <span className="block my-6">
          <Image
            src={src}
            alt={alt || ""}
            width={800}
            height={400}
            className="rounded-lg border border-border"
            {...props}
          />
        </span>
      );
    },

    // Text formatting
    em: ({ children, ...props }: React.ComponentProps<"em">) => {
      return (
        <em
          className="italic"
          {...props}
        >
          {children}
        </em>
      );
    },

    strong: ({ children, ...props }: React.ComponentProps<"strong">) => {
      return (
        <strong
          className="font-bold"
          {...props}
        >
          {children}
        </strong>
      );
    },

    // Task lists (GitHub-style checkboxes)
    input: ({ type, checked, ...props }: React.ComponentProps<"input">) => {
      if (type === "checkbox") {
        return (
          <input
            type="checkbox"
            checked={checked}
            disabled
            className="mr-2 accent-primary"
            {...props}
          />
        );
      }
      return <input type={type} {...props} />;
    },

    // Keyboard keys
    kbd: ({ children, ...props }: React.ComponentProps<"kbd">) => {
      return (
        <kbd
          className="inline-flex items-center rounded border border-border bg-muted px-2 py-1 text-xs font-mono"
          {...props}
        >
          {children}
        </kbd>
      );
    },

    // Mark/highlight
    mark: ({ children, ...props }: React.ComponentProps<"mark">) => {
      return (
        <mark
          className="bg-yellow-200 dark:bg-yellow-800 px-1 rounded"
          {...props}
        >
          {children}
        </mark>
      );
    },

    // Strikethrough
    del: ({ children, ...props }: React.ComponentProps<"del">) => {
      return (
        <del
          className="line-through opacity-70"
          {...props}
        >
          {children}
        </del>
      );
    },

    // Subscript and superscript
    sub: ({ children, ...props }: React.ComponentProps<"sub">) => {
      return (
        <sub
          className="text-xs"
          {...props}
        >
          {children}
        </sub>
      );
    },

    sup: ({ children, ...props }: React.ComponentProps<"sup">) => {
      return (
        <sup
          className="text-xs"
          {...props}
        >
          {children}
        </sup>
      );
    },

    // Details and summary for collapsible content
    details: ({ children, ...props }: React.ComponentProps<"details">) => {
      return (
        <details
          className="my-4 rounded border border-border p-4"
          {...props}
        >
          {children}
        </details>
      );
    },

    summary: ({ children, ...props }: React.ComponentProps<"summary">) => {
      return (
        <summary
          className="cursor-pointer font-semibold hover:text-primary"
          {...props}
        >
          {children}
        </summary>
      );
    },
  };

}
