{"$schema": "https://ui.shadcn.com/schema/registry-item.json", "name": "menu-vertical", "title": "Menu Vertical", "description": "An animated vertical menu component that reveals a sliding arrow icon and animated text with optional skew.", "author": "Reche Soares", "type": "registry:ui", "dependencies": ["motion"], "devDependencies": [], "registryDependencies": [], "cssVars": {"dark": {}, "light": {}}, "files": [{"path": "menu-vertical.tsx", "content": "\"use client\";\n\nimport { motion } from \"motion/react\";\nimport { ArrowRight } from \"lucide-react\";\n\nimport Link from \"next/link\";\n\ntype MenuItem = {\n  label: string;\n  href: string;\n};\n\ninterface MenuVerticalProps {\n  menuItems: MenuItem[];\n  color?: string;\n  skew?: number;\n}\n\nconst MotionLink = motion.create(Link);\n\nexport const MenuVertical = ({\n  menuItems = [],\n  color = \"#ff6900\",\n  skew = 0,\n}: MenuVerticalProps) => {\n  return (\n    <div className=\"flex w-fit flex-col gap-4 px-10\">\n      {menuItems.map((item, index) => (\n        <motion.div\n          key={`${item.href}-${index}`}\n          className=\"group/nav flex items-center gap-2 cursor-pointer text-zinc-900 dark:text-zinc-50\"\n          initial=\"initial\"\n          whileHover=\"hover\"\n        >\n          <motion.div\n            variants={{\n              initial: { x: \"-100%\", color: \"inherit\", opacity: 0 },\n              hover: { x: 0, color, opacity: 1 },\n            }}\n            transition={{ duration: 0.3, ease: \"easeOut\" }}\n            className=\"z-0\"\n          >\n            <ArrowRight strokeWidth={3} className=\"size-10\" />\n          </motion.div>\n\n          <MotionLink\n            href={item.href}\n            variants={{\n              initial: { x: -40, color: \"inherit\" },\n              hover: { x: 0, color, skewX: skew },\n            }}\n            transition={{ duration: 0.3, ease: \"easeOut\" }}\n            className=\"font-semibold text-4xl no-underline\"\n          >\n            {item.label}\n          </MotionLink>\n        </motion.div>\n      ))}\n    </div>\n  );\n};\n", "type": "registry:ui"}]}