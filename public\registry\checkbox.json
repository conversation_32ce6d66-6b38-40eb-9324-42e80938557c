{"$schema": "https://ui.shadcn.com/schema/registry-item.json", "name": "checkbox", "title": "Checkbox", "description": "A smooth, animated checkbox component with customizable color, size, and duration.", "author": "Reche Soares", "type": "registry:ui", "dependencies": ["motion"], "devDependencies": [], "registryDependencies": [], "cssVars": {"dark": {}, "light": {}}, "files": [{"path": "checkbox.tsx", "content": "\"use client\";\nimport { motion } from \"motion/react\";\n\ninterface CheckBoxProps {\n  checked: boolean;\n  onClick: () => void;\n  size?: number;\n  color?: string;\n  duration?: number;\n}\n\nexport const CheckBox = ({\n  checked,\n  onClick,\n  size = 32,\n  color = \"#00e599\",\n  duration = 1,\n}: CheckBoxProps) => {\n  return (\n    <div className=\"select-none cursor-pointer\" onClick={onClick}>\n      <svg width={size} height={size} viewBox=\"0 0 40 40\" fill=\"none\">\n        <motion.path\n          d=\"M 2.45 24.95 V 33.95 C 2.45 35.9382 4.0618 37.55 6.05 37.55 H 33.95 C 35.9382 37.55 37.55 35.9382 37.55 33.95 V 6.05 C 37.55 4.0618 35.9382 2.45 33.95 2.45 H 6.05 C 4.0618 2.45 2.45 4.0618 2.45 6.05 V 22.0617 C 2.45 23.0443 2.8516 23.9841 3.5616 24.6633 L 10.0451 30.8649 C 11.5404 32.2952 13.9308 32.1735 15.2731 30.5988 L 36.2 6.05\"\n          stroke={color}\n          strokeLinecap=\"round\"\n          strokeWidth={3}\n          animate={{\n            strokeDasharray: checked ? 150 : 132,\n            strokeDashoffset: checked ? -134 : 0,\n          }}\n          transition={{\n            duration,\n            ease: \"easeInOut\",\n          }}\n        />\n      </svg>\n    </div>\n  );\n};\n", "type": "registry:ui"}]}