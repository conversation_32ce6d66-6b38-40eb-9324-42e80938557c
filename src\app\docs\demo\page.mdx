import { CodeBlock } from "@/components/site/code-block";
import { ComponentRenderer } from "@/components/site/component-renderer";
import { Cli } from "@/components/site/cli";
import {
  Ta<PERSON>,
  <PERSON><PERSON>List,
  TabsTrigger,
  TabsContent,
} from "@/components/site/tabs.tsx";
import { PropsTable } from "@/components/site/props-table.tsx";

import Demo from "./demo.tsx";

# Demo

This is a demo page.

## Examples

### Demo

<Tabs defaultValue="preview" className="">
  <TabsList>
    <TabsTrigger value="preview">Preview</TabsTrigger>
    <TabsTrigger value="code">Code</TabsTrigger>
  </TabsList>
  <TabsContent value="preview">
    <ComponentRenderer component={<Demo />} />
  </TabsContent>
  <TabsContent value="code">
    <CodeBlock filePath="src/app/docs/demo/demo.tsx" />
  </TabsContent>
</Tabs>


## Installation

<Tabs defaultValue="cli" className="">
  <TabsList>
    <TabsTrigger value="cli">CLI</TabsTrigger>
    <TabsTrigger value="manual">Manual</TabsTrigger>
  </TabsList>
  <TabsContent value="cli">
    <Cli command={`add "http://localhost:3000/registry/demo.json"`} />
  </TabsContent>

  <TabsContent value="manual">
    <CodeBlock filePath="src/app/docs/demo/demo.tsx" />
  </TabsContent>
</Tabs>