{"$schema": "https://ui.shadcn.com/schema/registry-item.json", "name": "menu-fluid", "title": "<PERSON><PERSON>", "description": "A tabular navigation with seamless fluid hover animation.", "author": "Reche Soares", "type": "registry:ui", "dependencies": ["motion"], "devDependencies": [], "registryDependencies": [], "cssVars": {"dark": {}, "light": {}}, "files": [{"path": "menu-fluid.tsx", "content": "\"use client\";\n\nimport Link from \"next/link\";\nimport { useState } from \"react\";\nimport { motion } from \"motion/react\";\nimport { cn } from \"@/lib/utils\";\n\ntype MenuItem = {\n  label: string;\n  href: string;\n};\n\ninterface MenuFluidProps {\n  menuItems: MenuItem[];\n  className?: string;\n  indicatorClassName?: string;\n}\n\nexport const MenuFluid = ({\n  menuItems,\n  className,\n  indicatorClassName,\n}: MenuFluidProps) => {\n  const [hovered, setHovered] = useState<number | null>(null);\n  return (\n    <div\n      className={cn(\n        \"rounded-full p-1 flex items-center border border-zinc-300 dark:border-zinc-600\",\n        className\n      )}\n    >\n      {menuItems.map((item, index) => (\n        <Link\n          onMouseEnter={() => setHovered(index)}\n          onMouseLeave={() => setHovered(null)}\n          className=\"py-2 md:py-3 px-4 md:px-8 relative text-zinc-900 dark:text-zinc-50\"\n          key={`${item.label}-${index}`}\n          href={item.href}\n        >\n          {hovered === index && (\n            <motion.div\n              layoutId=\"fluid\"\n              transition={{ duration: 0.2, ease: \"linear\" }}\n              className={cn(\n                \"absolute inset-0 rounded-full bg-zinc-100 dark:bg-zinc-700\",\n                indicatorClassName\n              )}\n            />\n          )}\n          <span className=\"font-semibold text-sm z-20 relative\">\n            {item.label}\n          </span>\n        </Link>\n      ))}\n    </div>\n  );\n};\n", "type": "registry:ui"}]}