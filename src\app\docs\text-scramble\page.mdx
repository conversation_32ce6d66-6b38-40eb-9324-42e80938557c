import { CodeBlock } from "@/components/site/code-block";
import { ComponentRenderer } from "@/components/site/component-renderer";
import { Cli } from "@/components/site/cli";
import {
  Ta<PERSON>,
  <PERSON><PERSON>List,
  TabsTrigger,
  TabsContent,
} from "@/components/site/tabs.tsx";
import { PropsTable } from "@/components/site/props-table.tsx";

import TextScrambleBasic from "./text-scramble-basic.tsx";
import TextScrambleCustomChars from "./text-scramble-custom-chars.tsx";

# Text Scramble

Animates text by cycling through random characters, progressively revealing the original text to create a dynamic scramble effect.

## Examples

### Text Scramble Basic

Simple usage with default character set and speed.

<Tabs defaultValue="preview" className="">
  <TabsList>
    <TabsTrigger value="preview">Preview</TabsTrigger>
    <TabsTrigger value="code">Code</TabsTrigger>
  </TabsList>
  <TabsContent value="preview">
    <ComponentRenderer reTrigger={true} component={<TextScrambleBasic />} />
  </TabsContent>
  <TabsContent value="code">
    <CodeBlock filePath="src/app/docs/text-scramble/text-scramble-basic.tsx" />
  </TabsContent>
</Tabs>

### Custom CharacterSet

Using custom charset with slower animation speed.

<Tabs defaultValue="preview" className="">
  <TabsList>
    <TabsTrigger value="preview">Preview</TabsTrigger>
    <TabsTrigger value="code">Code</TabsTrigger>
  </TabsList>
  <TabsContent value="preview">
    <ComponentRenderer
      reTrigger={true}
      component={<TextScrambleCustomChars />}
    />
  </TabsContent>
  <TabsContent value="code">
    <CodeBlock filePath="src/app/docs/text-scramble/text-scramble-custom-chars.tsx" />
  </TabsContent>
</Tabs>

## Installation

<Tabs defaultValue="cli" className="">
  <TabsList>
    <TabsTrigger value="cli">CLI</TabsTrigger>
    <TabsTrigger value="manual">Manual</TabsTrigger>
  </TabsList>
  <TabsContent value="cli">
    <Cli command={`add "https://berlix.vercel.app/registry/text-scramble.json"`} />
  </TabsContent>

  <TabsContent value="manual">
    <CodeBlock filePath="src/components/core/text-scramble.tsx" />
  </TabsContent>
</Tabs>

## Component Props

<PropsTable
  props={[
    {
      name: "children",
      type: "string",
      required: true,
      description: "Text to animate with scrambling effect",
    },
    {
      name: "speed",
      type: "number",
      default: 50,
      description: "Interval in ms between scramble steps",
    },
    {
      name: "characterSet",
      type: "string",
      default: `ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789`,
      description: "Characters used for scrambling",
    },
    {
      name: "className",
      type: "string",
      description: "Optional styling classes",
    },
  ]}
/>
