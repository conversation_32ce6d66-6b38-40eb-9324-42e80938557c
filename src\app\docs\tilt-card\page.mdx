import { CodeB<PERSON> } from "@/components/site/code-block";
import { ComponentRenderer } from "@/components/site/component-renderer";
import { Cli } from "@/components/site/cli";
import {
  Tabs,
  TabsList,
  TabsTrigger,
  TabsContent,
} from "@/components/site/tabs.tsx";
import { PropsTable } from "@/components/site/props-table.tsx";

import TiltCardBasic from "./tilt-card-basic";
import TiltCardCTA from "./tilt-card-cta";

# Tilt Card

A 3D interactive card component that responds to mouse movement with smooth tilt and elevation effects.

## Examples

### Tilt Card Basic

Basic ussage using the default values.

<Tabs defaultValue="preview" className="">
  <TabsList>
    <TabsTrigger value="preview">Preview</TabsTrigger>
    <TabsTrigger value="code">Code</TabsTrigger>
  </TabsList>
  <TabsContent value="preview">
    <ComponentRenderer component={<TiltCardBasic />} />
  </TabsContent>
  <TabsContent value="code">
    <CodeBlock filePath="src/app/docs/tilt-card/tilt-card-basic.tsx" />
  </TabsContent>
</Tabs>

### Tilt Card CTA

Uses custom tilt range and depth values.

<Tabs defaultValue="preview" className="">
  <TabsList>
    <TabsTrigger value="preview">Preview</TabsTrigger>
    <TabsTrigger value="code">Code</TabsTrigger>
  </TabsList>
  <TabsContent value="preview">
    <ComponentRenderer component={<TiltCardCTA />} />
  </TabsContent>
  <TabsContent value="code">
    <CodeBlock filePath="src/app/docs/tilt-card/tilt-card-cta.tsx" />
  </TabsContent>
</Tabs>

## Installation

<Tabs defaultValue="cli" className="">
  <TabsList>
    <TabsTrigger value="cli">CLI</TabsTrigger>
    <TabsTrigger value="manual">Manual</TabsTrigger>
  </TabsList>
  <TabsContent value="cli">
    <Cli command={`add "https://berlix.vercel.app/registry/tilt-card.json"`} />
  </TabsContent>

  <TabsContent value="manual">
    <CodeBlock filePath="src/components/core/tilt-card.tsx" />
  </TabsContent>
</Tabs>

## Component Props

<PropsTable
  props={[
    {
      name: "children",
      type: "ReactNode",
      required: true,
      description: "Content inside the tilt card.",
    },
    {
      name: "range",
      type: "number",
      default: "32",
      description: "Controls the tilt range.",
    },
    {
      name: "depth",
      type: "number",
      default: "80",
      description: "Controls the z-elevation on hover.",
    },
    {
      name: "containerClassName",
      type: "string",
      description: "Optional class for styling the card container.",
    },
  ]}
/>
