{"$schema": "https://ui.shadcn.com/schema/registry-item.json", "name": "text-circle", "title": "Text Circle", "description": "A component that displays text in a circular layout with continuous rotation.", "author": "Reche Soares", "type": "registry:ui", "dependencies": ["motion"], "devDependencies": [], "registryDependencies": [], "cssVars": {"dark": {}, "light": {}}, "files": [{"path": "text-circle.tsx", "content": "\"use client\";\nimport { motion } from \"motion/react\";\nimport { cn } from \"@/lib/utils\";\n\ninterface TextCircleProps {\n  text: string;\n  duration?: number;\n  className?: string;\n}\n\nexport const TextCircle = ({\n  text,\n  duration = 20,\n  className,\n}: TextCircleProps) => {\n  const letters = Array.from(text);\n\n  return (\n    <motion.div\n      initial={{ rotate: 0 }}\n      animate={{ rotate: 360 }}\n      transition={{\n        repeat: Infinity,\n        repeatType: \"loop\",\n        duration,\n        ease: \"linear\",\n      }}\n      className={cn(\n        \"relative rounded-full w-[200px] h-[200px] text-zinc-900 dark:text-zinc-50 font-semibold text-center text-2xl\",\n        className\n      )}\n    >\n      {letters.map((letter, i) => {\n        const angle = (360 / letters.length) * i;\n\n        const factor = Number((Math.PI / letters.length).toFixed(0));\n        const x = factor * i;\n        const y = factor * i;\n        const transform = `rotateZ(${angle}deg) translate3d(${x}px, ${y}px, 0)`;\n\n        return (\n          <span key={i} className=\"absolute inset-0\" style={{ transform }}>\n            {letter}\n          </span>\n        );\n      })}\n    </motion.div>\n  );\n};\n", "type": "registry:ui"}]}