{"$schema": "https://ui.shadcn.com/schema/registry-item.json", "name": "text-scramble", "title": "Text Scramble", "description": "Animates text by cycling through random characters, progressively revealing the original text to create a dynamic scramble effect.", "author": "Reche Soares", "type": "registry:ui", "dependencies": ["motion"], "devDependencies": [], "registryDependencies": [], "cssVars": {"dark": {}, "light": {}}, "files": [{"path": "text-scramble.tsx", "content": "\"use client\";\nimport { useEffect, useState } from \"react\";\nimport { motion } from \"framer-motion\";\n\ninterface TextScrambleProps {\n  children: string;\n  speed?: number;\n  characterSet?: string;\n  className?: string;\n}\n\nconst DEFAULT_CHARS =\n  \"ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789\";\n\nfunction getRandomChar(charSet: string) {\n  return charSet[Math.floor(Math.random() * charSet.length)];\n}\n\nexport const TextScramble = ({\n  children,\n  speed = 50,\n  characterSet = DEFAULT_CHARS,\n  className,\n  ...motionProps\n}: TextScrambleProps) => {\n  const [text, setText] = useState(children);\n\n  useEffect(() => {\n    let step = 0;\n    const interval = setInterval(() => {\n      let scrambled = \"\";\n\n      for (let i = 0; i < children.length; i++) {\n        if (i < step) {\n          scrambled += children[i];\n        } else if (children[i] === \" \") {\n          scrambled += \" \";\n        } else {\n          scrambled += getRandomChar(characterSet);\n        }\n      }\n\n      setText(scrambled);\n      step++;\n\n      if (step > children.length) {\n        clearInterval(interval);\n        setText(children);\n      }\n    }, speed);\n\n    return () => clearInterval(interval);\n  }, [children, speed, characterSet]);\n\n  return (\n    <motion.span className={className} {...motionProps}>\n      {text}\n    </motion.span>\n  );\n};\n", "type": "registry:ui"}]}