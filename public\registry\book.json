{"$schema": "https://ui.shadcn.com/schema/registry-item.json", "name": "book", "title": "Book", "description": "A sleek and interactive 3D book component that flips open on hover, featuring smooth motion, layered depth, and customizable content.", "author": "Reche Soares", "type": "registry:ui", "dependencies": ["motion"], "devDependencies": [], "registryDependencies": [], "cssVars": {"dark": {}, "light": {}}, "files": [{"path": "book.tsx", "content": "\"use client\";\n\nimport { motion, useMotionValue, useSpring } from \"motion/react\";\nimport { cn } from \"@/lib/utils\";\n\ninterface BookProps {\n  content: React.ReactNode;\n  cover: React.ReactNode;\n  backOfCover?: React.ReactNode;\n  rotate?: number;\n  coverRotate?: number;\n  color?: string;\n  className?: string;\n}\n\nexport const Book = ({\n  content,\n  cover,\n  backOfCover,\n  rotate = -30,\n  coverRotate = -100,\n  className,\n  color = \"#e30012\",\n}: BookProps) => {\n  const rotatePage = useMotionValue(0);\n  const rotateSpring = useSpring(rotatePage, {\n    stiffness: 100,\n    damping: 40,\n  });\n\n  const handleMouseEnter = () => rotatePage.set(coverRotate);\n  const handleMouseLeave = () => rotatePage.set(0);\n\n  return (\n    <div style={{ perspective: \"1000px\" }}>\n      <motion.div\n        onMouseEnter={handleMouseEnter}\n        onMouseLeave={handleMouseLeave}\n        style={{\n          rotateY: rotate,\n        }}\n        transition={{ duration: 1, ease: \"easeInOut\" }}\n        className={cn(\"relative w-52 h-80 transform-3d\", className)}\n      >\n        <motion.div\n          style={{\n            rotateY: rotateSpring,\n            z: 15,\n          }}\n          className=\"z-10 shadow-2xl w-full h-full absolute transform-3d origin-left\"\n        >\n          <div\n            style={{ transform: \"rotateY(180deg)\" }}\n            className={cn(\n              \"absolute w-full h-full top-0 left-0 overflow-hidden rounded-r-xs bg-zinc-900 backface-hidden\"\n            )}\n          >\n            {backOfCover}\n          </div>\n\n          <div\n            className={cn(\n              \"absolute w-full h-full top-0 left-0 overflow-hidden rounded-l-xs backface-hidden\"\n            )}\n          >\n            {cover}\n          </div>\n        </motion.div>\n\n        <motion.div\n          style={{ z: 14 }}\n          className=\"absolute z-20 top-[1%] left-0 w-[calc(100%-3%)] h-[calc(100%-2%)] bg-zinc-50 overflow-hidden\"\n        >\n          {content}\n        </motion.div>\n\n        <div className=\"absolute top-[1%] -right-[4%] h-[calc(100%-2%)] w-[29px] transform rotate-y-90  bg-gradient-to-r from-zinc-50 via-zinc-300 to-zinc-50 bg-[length:5%_100%] bg-repeat-x shadow-2xl\" />\n\n        <div\n          style={{ background: color }}\n          className=\"absolute top-0 left-0 h-full w-[30px] transform -rotate-y-90 -translate-x-[50%]  bg-red-600\"\n        />\n\n        <motion.div\n          style={{ z: -15, background: color }}\n          className=\"absolute top-0 left-0 w-full rounded-r-xs h-full bg-red-600 shadow-lg \"\n        />\n      </motion.div>\n    </div>\n  );\n};\n", "type": "registry:ui"}]}