{"$schema": "https://ui.shadcn.com/schema/registry-item.json", "name": "text-ripple", "title": "Text Ripple", "description": "A animated text effect that scales characters on hover with a ripple animation. Customize the maximum scale and ripple falloff to adjust the intensity and spread of the effect.", "author": "Reche Soares", "type": "registry:ui", "dependencies": ["motion"], "devDependencies": [], "registryDependencies": [], "cssVars": {"dark": {}, "light": {}}, "files": [{"path": "text-ripple.tsx", "content": "\"use client\";\nimport { cn } from \"@/lib/utils\";\nimport { motion } from \"framer-motion\";\nimport { useState } from \"react\";\n\ninterface TextRippleProps {\n  children: string;\n  className?: string;\n  maxScale?: number;\n  falloff?: number;\n}\n\nexport const TextRipple = ({\n  children,\n  className,\n  maxScale = 2,\n  falloff = 0.3,\n}: TextRippleProps) => {\n  const [hoveredIndex, setHoveredIndex] = useState<number | null>(null);\n  const chars = children.split(\"\");\n\n  const getScaleY = (index: number) => {\n    if (hoveredIndex === null) return 1;\n    const distance = Math.abs(index - hoveredIndex);\n    return Math.max(1, maxScale - distance * falloff);\n  };\n\n  return (\n    <div className={cn(\"relative font-bold text-2xl\", className)}>\n      {chars.map((s, index) => (\n        <motion.span\n          onMouseEnter={() => setHoveredIndex(index)}\n          onMouseLeave={() => setHoveredIndex(null)}\n          className=\"inline-block origin-bottom leading-[0.7]\"\n          animate={{ scaleY: getScaleY(index) }}\n          key={`${s}-${index}`}\n        >\n          {s === \" \" ? \"\\u00A0\" : s}\n        </motion.span>\n      ))}\n    </div>\n  );\n};\n", "type": "registry:ui"}]}