import { CodeBlock } from "@/components/site/code-block";
import { ComponentRenderer } from "@/components/site/component-renderer";
import { Cli } from "@/components/site/cli";
import {
  Tabs,
  TabsList,
  TabsTrigger,
  TabsContent,
} from "@/components/site/tabs.tsx";
import { PropsTable } from "@/components/site/props-table";

import TextRevealLetters from "./text-reveal-letters";
import TextRevealWords from "./text-reveal-words";

# Text Reveal

A text animation component that reveals words or letters with blur, offset, and staggered motion effects.

## Examples

### Title Reveal

Title animation revealing each letter with blur and vertical motion.

<Tabs defaultValue="preview" className="">
  <TabsList>
    <TabsTrigger value="preview">Preview</TabsTrigger>
    <TabsTrigger value="code">Code</TabsTrigger>
  </TabsList>
  <TabsContent value="preview">
    <ComponentRenderer reTrigger component={<TextRevealLetters />} />
  </TabsContent>
  <TabsContent value="code">
    <CodeBlock filePath="src/app/docs/text-reveal/text-reveal-letters.tsx" />
  </TabsContent>
</Tabs>

### Text Reveal with Custom Props

Reveals each word with custom blur, timing, and reveal direction.

<Tabs defaultValue="preview" className="">
  <TabsList>
    <TabsTrigger value="preview">Preview</TabsTrigger>
    <TabsTrigger value="code">Code</TabsTrigger>
  </TabsList>
  <TabsContent value="preview">
    <ComponentRenderer reTrigger component={<TextRevealWords />} />
  </TabsContent>
  <TabsContent value="code">
    <CodeBlock filePath="src/app/docs/text-reveal/text-reveal-words.tsx" />
  </TabsContent>
</Tabs>

## Installation

<Tabs defaultValue="cli" className="">
  <TabsList>
    <TabsTrigger value="cli">CLI</TabsTrigger>
    <TabsTrigger value="manual">Manual</TabsTrigger>
  </TabsList>
  <TabsContent value="cli">
    <Cli command={`add "https://berlix.vercel.app/registry/text-reveal.json"`} />
  </TabsContent>

  <TabsContent value="manual">
    <CodeBlock filePath="src/components/core/text-reveal.tsx" />
  </TabsContent>
</Tabs>

## Component Props

<PropsTable
  props={[
    {
      name: "children",
      type: "string",
      required: true,
      description: "Text to be revealed",
    },
    {
      name: "className",
      type: "string",
      description: "Optional class names for styling the text.",
    },
    {
      name: "blur",
      type: "number",
      default: 10,
      description: "Initial blur amount in pixels before the animation starts",
    },
    {
      name: "delay",
      type: "number",
      default: 0.1,
      description: "Delay in seconds between revealing each word or letter",
    },
    {
      name: "duration",
      type: "number",
      default: 1,
      description: "Animation duration in seconds for each word or letter",
    },
    {
      name: "from",
      type: `'top' | 'bottom'`,
      default: `'bottom'`,
      description: "Animation Direction from which each word or letter enters",
    },
    {
      name: "split",
      type: `'word' | 'letter'`,
      default: `'word'`,
      description: "Whether to animate the text by words or individual letters",
    },
  ]}
/>
