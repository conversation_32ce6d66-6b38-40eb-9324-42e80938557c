# Markdown Demo - All Elements

This page demonstrates all markdown elements rendered perfectly with our enhanced MDX components.

## Headings

# Heading 1
## Heading 2
### Heading 3
#### Heading 4
##### Heading 5
###### Heading 6

---

## Text Formatting

**Bold text** and *italic text* and ***bold italic text***.

Regular text with `inline code` and links.

---

## Links

[Internal link](/docs) and [external link](https://example.com).

---

## Lists

### Unordered List
- First item
- Second item
  - Nested item
  - Another nested item
- Third item

### Ordered List
1. First item
2. Second item
   1. Nested item
   2. Another nested item
3. Third item

---

## Blockquotes

> This is a blockquote with some important information.

---

## Code

### Inline Code
Use `console.log()` to print to the console.

### Code Blocks

```javascript
// JavaScript example
function greet(name) {
  console.log(`Hello, ${name}!`);
}

greet("World");
```

```typescript
// TypeScript example
interface User {
  id: number;
  name: string;
  email: string;
}

const user: User = {
  id: 1,
  name: "<PERSON>",
  email: "<EMAIL>"
};
```

---

## Tables

| Feature | Supported | Notes |
|---------|-----------|-------|
| Headers | ✅ | Auto-generated IDs |
| Links | ✅ | Internal and external |
| Code | ✅ | Syntax highlighting |
| Tables | ✅ | Responsive design |

---

## Horizontal Rules

Above this line.

---

Below this line.

---

## Conclusion

This demonstrates the major markdown elements with beautiful styling and functionality. Each element is:

- ✅ **Properly styled** with Tailwind CSS
- ✅ **Responsive** and mobile-friendly
- ✅ **Accessible** with proper ARIA labels
- ✅ **Interactive** where appropriate
- ✅ **Consistent** with your design system
