# Markdown Demo - All Elements

This page demonstrates all markdown elements rendered perfectly with our enhanced MDX components.

## Headings

# Heading 1
## Heading 2  
### Heading 3
#### Heading 4
##### Heading 5
###### Heading 6

---

## Text Formatting

**Bold text** and *italic text* and ***bold italic text***.

~~Strikethrough text~~ and <mark>highlighted text</mark>.

Regular text with `inline code` and <kbd>Ctrl</kbd> + <kbd>C</kbd> keyboard shortcuts.

Subscript: H<sub>2</sub>O and Superscript: E=mc<sup>2</sup>

---

## Links

[Internal link](/docs) and [external link](https://example.com) and [anchor link](#headings).

---

## Lists

### Unordered List
- First item
- Second item
  - Nested item
  - Another nested item
- Third item

### Ordered List
1. First item
2. Second item
   1. Nested item
   2. Another nested item
3. Third item

### Task List
- [x] Completed task
- [ ] Incomplete task
- [x] Another completed task

---

## Blockquotes

> This is a blockquote. It can contain multiple paragraphs and other markdown elements.
> 
> > This is a nested blockquote.

---

## Code

### Inline Code
Use `console.log()` to print to the console.

### Code Blocks

```javascript
// JavaScript example
function greet(name) {
  console.log(`Hello, ${name}!`);
}

greet("World");
```

```typescript
// TypeScript example
interface User {
  id: number;
  name: string;
  email: string;
}

const user: User = {
  id: 1,
  name: "John Doe",
  email: "<EMAIL>"
};
```

```css
/* CSS example */
.button {
  background-color: #007bff;
  color: white;
  padding: 0.5rem 1rem;
  border: none;
  border-radius: 0.25rem;
  cursor: pointer;
  transition: background-color 0.2s;
}

.button:hover {
  background-color: #0056b3;
}
```

```bash
# Bash example
npm install
npm run dev
```

---

## Tables

| Feature | Supported | Notes |
|---------|-----------|-------|
| Headers | ✅ | Auto-generated IDs |
| Links | ✅ | Internal and external |
| Code | ✅ | Syntax highlighting |
| Tables | ✅ | Responsive design |
| Images | ✅ | Next.js optimization |

| Left Aligned | Center Aligned | Right Aligned |
|:-------------|:--------------:|--------------:|
| Left | Center | Right |
| Text | Text | Text |

---

## Images

![Sample Image](https://via.placeholder.com/600x300/007bff/ffffff?text=Sample+Image)

---

## Horizontal Rules

Above this line.

---

Below this line.

---

## Details/Summary (Collapsible Content)

<details>
<summary>Click to expand</summary>

This content is hidden by default and can be expanded by clicking the summary.

You can include any markdown content here:

- Lists
- **Bold text**
- `Code`
- Links

</details>

---

## HTML Elements

You can also use HTML elements directly:

<div style="padding: 1rem; background: #f0f0f0; border-radius: 0.5rem; margin: 1rem 0;">
  This is a custom HTML div with inline styles.
</div>

---

## Complex Example

Here's a more complex example combining multiple elements:

### API Documentation

The `fetchUser` function retrieves user data from the API.

```typescript
async function fetchUser(id: number): Promise<User | null> {
  try {
    const response = await fetch(`/api/users/${id}`);
    
    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }
    
    const user: User = await response.json();
    return user;
  } catch (error) {
    console.error('Failed to fetch user:', error);
    return null;
  }
}
```

**Parameters:**
- `id` (number): The user ID to fetch

**Returns:**
- `Promise<User | null>`: The user object or null if not found

**Example usage:**

```javascript
const user = await fetchUser(123);
if (user) {
  console.log(`Welcome, ${user.name}!`);
} else {
  console.log('User not found');
}
```

> **Note:** This function requires authentication. Make sure to include the proper headers.

---

## Conclusion

This demonstrates all the major markdown elements with beautiful styling and functionality. Each element is:

- ✅ **Properly styled** with Tailwind CSS
- ✅ **Responsive** and mobile-friendly  
- ✅ **Accessible** with proper ARIA labels
- ✅ **Interactive** where appropriate (copy buttons, etc.)
- ✅ **Consistent** with your design system
