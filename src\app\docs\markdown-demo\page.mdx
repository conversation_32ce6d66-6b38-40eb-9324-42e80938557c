# Markdown Demo - All Elements

This page demonstrates all markdown elements rendered perfectly with our enhanced MDX components.

## Headings

# Heading 1
## Heading 2
### Heading 3
#### Heading 4
##### Heading 5
###### Heading 6

---

## Text Formatting

**Bold text** and *italic text* and ***bold italic text***.

~~Strikethrough text~~ and <mark>highlighted text</mark>.

Regular text with `inline code` and <kbd>Ctrl</kbd> + <kbd>C</kbd> keyboard shortcuts.

Subscript: H<sub>2</sub>O and Superscript: E=mc<sup>2</sup>

---

## Links

[Internal link](/docs) and [external link](https://example.com).

---

## Lists

### Unordered List
- First item
- Second item
  - Nested item
  - Another nested item
- Third item

### Ordered List
1. First item
2. Second item
   1. Nested item
   2. Another nested item
3. Third item

### Task List
- [x] Completed task
- [ ] Incomplete task
- [x] Another completed task
- [ ] Task with **bold** text
- [x] Task with `code`

---

## Blockquotes

> This is a blockquote with some important information.

---

## Code

### Inline Code
Use `console.log()` to print to the console.

### Code Blocks

```javascript
// JavaScript example
function greet(name) {
  console.log(`Hello, ${name}!`);
}

greet("World");
```

```typescript
// TypeScript example
interface User {
  id: number;
  name: string;
  email: string;
}

const user: User = {
  id: 1,
  name: "John Doe",
  email: "<EMAIL>"
};
```

---

## Tables

### Basic Table

| Feature | Supported | Notes |
|---------|-----------|-------|
| Headers | ✅ | Auto-generated IDs |
| Links | ✅ | Internal and external |
| Code | ✅ | Syntax highlighting |
| Tables | ✅ | Responsive design |
| Images | ✅ | Next.js optimization |

### Aligned Table

| Left Aligned | Center Aligned | Right Aligned |
|:-------------|:--------------:|--------------:|
| Left | Center | Right |
| Text | Text | Text |
| More content | More content | More content |

### Complex Table

| Name | Role | Experience | Skills | Location |
|------|------|------------|--------|----------|
| John Doe | Frontend Developer | 5 years | React, TypeScript, CSS | New York |
| Jane Smith | Backend Developer | 3 years | Node.js, Python, SQL | San Francisco |
| Bob Johnson | Full Stack | 7 years | React, Node.js, AWS | Remote |
| Alice Brown | UI/UX Designer | 4 years | Figma, Sketch, CSS | London |

---

## Horizontal Rules

Above this line.

---

Below this line.

---

## Conclusion

This demonstrates the major markdown elements with beautiful styling and functionality. Each element is:

- ✅ **Properly styled** with Tailwind CSS
- ✅ **Responsive** and mobile-friendly
- ✅ **Accessible** with proper ARIA labels
- ✅ **Interactive** where appropriate
- ✅ **Consistent** with your design system
