import { CodeB<PERSON> } from "@/components/site/code-block";
import { ComponentRenderer } from "@/components/site/component-renderer";
import { Cli } from "@/components/site/cli";
import {
  Ta<PERSON>,
  TabsList,
  TabsTrigger,
  TabsContent,
} from "@/components/site/tabs.tsx";
import { PropsTable } from "@/components/site/props-table.tsx";

import CheckBoxPreview from "./checkbox-preview";

# CheckBox

A smooth, animated checkbox component with customizable color, size, and duration.

## Preview

<Tabs defaultValue="preview" className="">
  <TabsList>
    <TabsTrigger value="preview">Preview</TabsTrigger>
    <TabsTrigger value="code">Code</TabsTrigger>
  </TabsList>
  <TabsContent value="preview">
    <ComponentRenderer component={<CheckBoxPreview />} />
  </TabsContent>
  <TabsContent value="code">
    <CodeBlock filePath="src/app/docs/checkbox/checkbox-preview.tsx" />
  </TabsContent>
</Tabs>

## Installation

<Tabs defaultValue="cli" className="">
  <TabsList>
    <TabsTrigger value="cli">CLI</TabsTrigger>
    <TabsTrigger value="manual">Manual</TabsTrigger>
  </TabsList>
  <TabsContent value="cli">
    <Cli command={`add "https://berlix.vercel.app/registry/checkbox.json"`} />
  </TabsContent>

  <TabsContent value="manual">
    <CodeBlock filePath="src/components/core/checkbox.tsx" />
  </TabsContent>
</Tabs>

## Component Props

<PropsTable
  props ={[
    {
      name: "checked",
      type: "boolean",
      required: true,
      description: "Controls whether the checkbox is checked.",
    },
    {
      name: "onClick",
      type: "() => void",
      required: true,
      description: "Function to call when the checkbox is clicked.",
    },
    {
      name: "size",
      type: "number",
      default: 32,
      description: "Size of the checkbox in pixels.",
    },
    {
      name: "color",
      type: "string",
      default: "#00e599",
      description: "Color of the checkbox stroke.",
    },
    {
      name: "duration",
      type: "number",
      default: 1,
      description: "Duration of the check animation in seconds.",
    }
  ]}

/>
