{"$schema": "https://ui.shadcn.com/schema/registry-item.json", "name": "text-reveal", "title": "Text Reveal", "description": "A text animation component that reveals words or letters with blur, offset, and staggered motion effects.", "author": "Reche Soares", "type": "registry:ui", "dependencies": ["motion"], "devDependencies": [], "registryDependencies": [], "cssVars": {"dark": {}, "light": {}}, "files": [{"path": "text-reveal.tsx", "content": "\"use client\";\n\nimport { cn } from \"@/lib/utils\";\nimport { motion } from \"motion/react\";\n\ninterface TextRevealProps {\n  children: string;\n  className?: string;\n  blur?: number;\n  delay?: number;\n  duration?: number;\n  from?: \"top\" | \"bottom\";\n  split?: \"word\" | \"letter\";\n}\n\nexport const TextReveal = ({\n  children,\n  className,\n  blur = 10,\n  delay = 0.1,\n  duration = 1,\n  from = \"bottom\",\n  split = \"word\",\n}: TextRevealProps) => {\n  const segments =\n    split === \"word\" ? children.split(\" \") : children.split(/(?=.)/);\n\n  return (\n    <div>\n      {segments.map((c, index) => (\n        <motion.span\n          key={`${c}-${index}`}\n          initial={{\n            opacity: 0,\n            y: from === \"bottom\" ? \"50%\" : \"-50%\",\n            filter: `blur(${blur}px)`,\n          }}\n          animate={{ opacity: 1, y: 0, filter: \"blur(0px)\" }}\n          transition={{\n            delay: index * delay,\n            duration,\n            ease: [0.18, 0.89, 0.82, 1.04],\n          }}\n          className={cn(\n            \"inline-flex leading-none\",\n            split === \"word\" ? \"mr-[0.2em]\" : \"\",\n            className\n          )}\n        >\n          {c === \" \" ? \"\\u00A0\" : c}\n        </motion.span>\n      ))}\n      <div className=\"sr-only\">{children}</div>\n    </div>\n  );\n};\n", "type": "registry:ui"}]}